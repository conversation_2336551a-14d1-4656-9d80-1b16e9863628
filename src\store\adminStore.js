import { create } from 'zustand';
import axios from 'axios';
import { ADMIN_ENDPOINTS } from '../lib/constants';

// Create axios instance with interceptor
const axiosInstance = axios.create({
    withCredentials: true,
});

// Add request interceptor to include auth token
axiosInstance.interceptors.request.use((config) => {
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

const useAdminStore = create((set, get) => ({
    // State
    users: [],
    companies: [],
    pendingCompanies: [],
    jobPosts: [],
    tests: [],
    settings: null,
    loading: false,
    error: null,

    // Pagination and filtering state
    usersPagination: { current: 1, pages: 1, total: 0 },
    companiesPagination: { current: 1, pages: 1, total: 0 },
    jobsPagination: { current: 1, pages: 1, total: 0 },
    testsPagination: { current: 1, pages: 1, total: 0 },

    // Statistics
    statistics: {
        totalUsers: 0,
        totalCompanies: 0,
        totalJobs: 0,
        totalTests: 0,
        activeUsers: 0,
        activeCompanies: 0,
        pendingCompanies: 0,
        flaggedJobs: 0,
        flaggedTests: 0
    },

    // Basic setters
    setLoading: (loading) => set({ loading }),
    setError: (error) => set({ error }),
    clearError: () => set({ error: null }),

    // Enhanced error handler
    handleError: (err, defaultMessage) => {
        const errorMessage = err?.response?.data?.error ||
            err?.response?.data?.message ||
            err?.message ||
            defaultMessage;
        set({ error: errorMessage });
        console.error('Admin Store Error:', err);
        return { success: false, error: errorMessage };
    },

    // USERS MANAGEMENT
    getUsers: async (params = {}) => {
        set({ loading: true, error: null });
        try {
            const queryParams = new URLSearchParams();

            // Add pagination parameters
            if (params.page) queryParams.append('page', params.page);
            if (params.limit) queryParams.append('limit', params.limit);

            // Add filter parameters
            if (params.search) queryParams.append('search', params.search);
            if (params.status) queryParams.append('status', params.status);
            if (params.role) queryParams.append('role', params.role);
            if (params.sortBy) queryParams.append('sortBy', params.sortBy);
            if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

            const url = `${ADMIN_ENDPOINTS.USERS}${queryParams.toString() ? `?${queryParams}` : ''}`;
            const res = await axiosInstance.get(url);

            set({
                users: res.data.data || res.data.users || [],
                usersPagination: {
                    current: res.data.pagination?.currentPage || 1,
                    pages: res.data.pagination?.totalPages || 1,
                    total: res.data.pagination?.total || 0
                }
            });

            return { success: true, data: res.data };
        } catch (err) {
            return get().handleError(err, 'Fetch users failed');
        } finally {
            set({ loading: false });
        }
    },

    getUserById: async (userId) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.get(ADMIN_ENDPOINTS.USER(userId));
            return {
                success: true,
                user: res.data.user,
                profile: res.data.profile // Include profile data if available
            };
        } catch (err) {
            return get().handleError(err, 'Fetch user failed');
        } finally {
            set({ loading: false });
        }
    },

    activateUser: async (userId) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(ADMIN_ENDPOINTS.ACTIVATE_USER(userId));

            // Update local state
            set((state) => ({
                users: state.users.map(user =>
                    user._id === userId ? { ...user, isVerified: true, isActive: true } : user
                ),
                statistics: {
                    ...state.statistics,
                    activeUsers: state.statistics.activeUsers + 1
                }
            }));

            return { success: true, message: res.data.message };
        } catch (err) {
            return get().handleError(err, 'Activate user failed');
        } finally {
            set({ loading: false });
        }
    },

    deactivateUser: async (userId) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(`${ADMIN_ENDPOINTS.USER(userId)}/deactivate`);

            // Update local state
            set((state) => ({
                users: state.users.map(user =>
                    user._id === userId ? { ...user, isVerified: false, isActive: false } : user
                ),
                statistics: {
                    ...state.statistics,
                    activeUsers: Math.max(0, state.statistics.activeUsers - 1)
                }
            }));

            return { success: true, message: res.data.message };
        } catch (err) {
            return get().handleError(err, 'Deactivate user failed');
        } finally {
            set({ loading: false });
        }
    },

    updateUserRole: async (userId, role) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.patch(ADMIN_ENDPOINTS.USER(userId), { role });

            // Update local state
            set((state) => ({
                users: state.users.map(user =>
                    user._id === userId ? { ...user, role } : user
                )
            }));

            return { success: true, message: res.data.message };
        } catch (err) {
            return get().handleError(err, 'Update user role failed');
        } finally {
            set({ loading: false });
        }
    },

    bulkUpdateUsers: async (userIds, updateData) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.patch(`${ADMIN_ENDPOINTS.USERS}/bulk`, {
                userIds,
                updateData
            });

            // Update local state
            set((state) => ({
                users: state.users.map(user =>
                    userIds.includes(user._id) ? { ...user, ...updateData } : user
                )
            }));

            return { success: true, message: res.data.message, updated: res.data.updated };
        } catch (err) {
            return get().handleError(err, 'Bulk update users failed');
        } finally {
            set({ loading: false });
        }
    },

    // COMPANIES MANAGEMENT - Filter from users instead of separate API
    getCompanies: async (params = {}) => {
        set({ loading: true, error: null });
        try {
            // Get users and filter for company role
            const usersResult = await get().getUsers({ role: 'company', ...params });

            if (usersResult.success) {
                // Transform user data to company format
                const companies = get().users
                    .filter(user => user.role === 'company')
                    .map(user => ({
                        _id: user._id,
                        companyName: user.companyName || user.name || 'Unknown Company',
                        email: user.email,
                        isVerified: user.isVerified,
                        isActive: user.isActive,
                        createdAt: user.createdAt,
                        updatedAt: user.updatedAt,
                        // Additional company fields if available
                        industry: user.industry,
                        companySize: user.companySize,
                        website: user.website,
                        description: user.description,
                        location: user.location,
                        // Map user fields to company fields
                        name: user.companyName || user.name,
                        status: user.isActive ? 'active' : 'inactive'
                    }));

                set({
                    companies,
                    companiesPagination: get().usersPagination // Use same pagination as users
                });

                return { success: true, data: { companies, pagination: get().usersPagination } };
            }

            return usersResult;
        } catch (err) {
            return get().handleError(err, 'Fetch companies failed');
        } finally {
            set({ loading: false });
        }
    },

    getPendingCompanies: async (params = {}) => {
        set({ loading: true, error: null });
        try {
            // Get pending companies from users with company role and not verified
            const usersResult = await get().getUsers({ role: 'company', isVerified: false, ...params });

            if (usersResult.success) {
                const pendingCompanies = get().users
                    .filter(user => user.role === 'company' && !user.isVerified)
                    .map(user => ({
                        _id: user._id,
                        companyName: user.companyName || user.name || 'Unknown Company',
                        email: user.email,
                        isVerified: user.isVerified,
                        isActive: user.isActive,
                        createdAt: user.createdAt,
                        updatedAt: user.updatedAt,
                        industry: user.industry,
                        companySize: user.companySize,
                        website: user.website,
                        description: user.description,
                        location: user.location,
                        name: user.companyName || user.name,
                        status: 'pending'
                    }));

                set({ pendingCompanies });
                return { success: true, data: { companies: pendingCompanies } };
            }

            return usersResult;
        } catch (err) {
            return get().handleError(err, 'Fetch pending companies failed');
        } finally {
            set({ loading: false });
        }
    },

    getCompanyDetails: async (companyId) => {
        set({ loading: true, error: null });
        try {
            // Get company details from user data
            const userResult = await get().getUserById(companyId);

            if (userResult.success && userResult.user.role === 'company') {
                const company = {
                    ...userResult.user,
                    companyName: userResult.user.companyName || userResult.user.name,
                    name: userResult.user.companyName || userResult.user.name,
                    status: userResult.user.isActive ? 'active' : 'inactive'
                };

                return { success: true, company };
            }

            return { success: false, error: 'Company not found' };
        } catch (err) {
            return get().handleError(err, 'Fetch company details failed');
        } finally {
            set({ loading: false });
        }
    },

    approveCompany: async (companyId, approvalData = {}) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(ADMIN_ENDPOINTS.COMPANY(companyId), approvalData);

            // Update local state
            set((state) => ({
                pendingCompanies: state.pendingCompanies.filter(company => company._id !== companyId),
                companies: [...state.companies, res.data.company],
                statistics: {
                    ...state.statistics,
                    totalCompanies: state.statistics.totalCompanies + 1,
                    activeCompanies: state.statistics.activeCompanies + 1,
                    pendingCompanies: Math.max(0, state.statistics.pendingCompanies - 1)
                }
            }));

            return { success: true, message: res.data.message, company: res.data.company };
        } catch (err) {
            return get().handleError(err, 'Approve company failed');
        } finally {
            set({ loading: false });
        }
    },

    rejectCompany: async (companyId, rejectionReason) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(`${ADMIN_ENDPOINTS.COMPANY(companyId)}/reject`, {
                reason: rejectionReason
            });

            // Update local state
            set((state) => ({
                pendingCompanies: state.pendingCompanies.filter(company => company._id !== companyId),
                statistics: {
                    ...state.statistics,
                    pendingCompanies: Math.max(0, state.statistics.pendingCompanies - 1)
                }
            }));

            return { success: true, message: res.data.message };
        } catch (err) {
            return get().handleError(err, 'Reject company failed');
        } finally {
            set({ loading: false });
        }
    },

    suspendCompany: async (companyId, suspensionReason) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(`${ADMIN_ENDPOINTS.COMPANY(companyId)}/suspend`, {
                reason: suspensionReason
            });

            // Update local state
            set((state) => ({
                companies: state.companies.map(company =>
                    company._id === companyId
                        ? { ...company, status: 'suspended', suspensionReason }
                        : company
                ),
                statistics: {
                    ...state.statistics,
                    activeCompanies: Math.max(0, state.statistics.activeCompanies - 1)
                }
            }));

            return { success: true, message: res.data.message };
        } catch (err) {
            return get().handleError(err, 'Suspend company failed');
        } finally {
            set({ loading: false });
        }
    },

    getCompanyDetails: async (companyId) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.get(ADMIN_ENDPOINTS.COMPANY(companyId));
            return { success: true, company: res.data.company };
        } catch (err) {
            return get().handleError(err, 'Fetch company details failed');
        } finally {
            set({ loading: false });
        }
    },

    // JOBS MANAGEMENT
    getJobs: async (params = {}) => {
        set({ loading: true, error: null });
        try {
            const queryParams = new URLSearchParams();

            Object.entries(params).forEach(([key, value]) => {
                if (value !== undefined && value !== null && value !== '') {
                    queryParams.append(key, value);
                }
            });

            const url = `${ADMIN_ENDPOINTS.JOBS}${queryParams.toString() ? `?${queryParams}` : ''}`;
            const res = await axiosInstance.get(url);

            // Transform the job data to match component expectations
            const transformedJobs = (res.data.data || res.data.jobs || []).map(job => ({
                ...job,
                // Normalize company data
                company: job.companyId ? {
                    name: job.companyId.companyName || job.companyId.name,
                    _id: job.companyId._id
                } : null,
                companyName: job.companyId?.companyName || job.companyName,
                // Normalize applications count
                applicationsCount: job.currentApplications || job.applicationsCount || 0,
                // Normalize status
                status: job.isActive ? 'active' : (job.status || 'inactive'),
                // Normalize salary display
                salary: job.salary ?
                    `${job.salary.currency || 'INR'} ${job.salary.min || 0} - ${job.salary.max || 0}` :
                    (job.salaryRange || 'Not specified')
            }));

            set({
                jobPosts: transformedJobs,
                jobsPagination: res.data.pagination || { current: 1, pages: 1, total: 0 }
            });

            return { success: true, data: res.data };
        } catch (err) {
            return get().handleError(err, 'Fetch jobs failed');
        } finally {
            set({ loading: false });
        }
    },

    getJobById: async (jobId) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.get(ADMIN_ENDPOINTS.JOB(jobId));

            // Transform the job data to match component expectations
            const job = res.data.job || res.data.data;
            const transformedJob = job ? {
                ...job,
                // Normalize company data
                company: job.companyId ? {
                    name: job.companyId.companyName || job.companyId.name,
                    _id: job.companyId._id
                } : null,
                companyName: job.companyId?.companyName || job.companyName,
                // Normalize applications count
                applicationsCount: job.currentApplications || job.applicationsCount || 0,
                // Normalize status
                status: job.isActive ? 'active' : (job.status || 'inactive'),
                // Normalize salary display
                salary: job.salary ?
                    `${job.salary.currency || 'INR'} ${job.salary.min || 0} - ${job.salary.max || 0}` :
                    (job.salaryRange || 'Not specified')
            } : null;

            return { success: true, job: transformedJob };
        } catch (err) {
            return get().handleError(err, 'Fetch job failed');
        } finally {
            set({ loading: false });
        }
    },

    flagJob: async (jobId, flagReason) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(ADMIN_ENDPOINTS.FLAG_JOB(jobId), {
                reason: flagReason
            });

            // Update local state
            set((state) => ({
                jobPosts: state.jobPosts.map(job =>
                    job._id === jobId
                        ? { ...job, isFlagged: true, flagReason, status: 'flagged' }
                        : job
                ),
                statistics: {
                    ...state.statistics,
                    flaggedJobs: state.statistics.flaggedJobs + 1
                }
            }));

            return { success: true, message: res.data.message };
        } catch (err) {
            return get().handleError(err, 'Flag job failed');
        } finally {
            set({ loading: false });
        }
    },

    unflagJob: async (jobId) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(`${ADMIN_ENDPOINTS.JOB(jobId)}/unflag`);

            // Update local state
            set((state) => ({
                jobPosts: state.jobPosts.map(job =>
                    job._id === jobId
                        ? { ...job, isFlagged: false, flagReason: null, status: 'active' }
                        : job
                ),
                statistics: {
                    ...state.statistics,
                    flaggedJobs: Math.max(0, state.statistics.flaggedJobs - 1)
                }
            }));

            return { success: true, message: res.data.message };
        } catch (err) {
            return get().handleError(err, 'Unflag job failed');
        } finally {
            set({ loading: false });
        }
    },

    bulkFlagJobs: async (jobIds, flagReason) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(`${ADMIN_ENDPOINTS.JOBS}/bulk-flag`, {
                jobIds,
                reason: flagReason
            });

            // Update local state
            set((state) => ({
                jobPosts: state.jobPosts.map(job =>
                    jobIds.includes(job._id)
                        ? { ...job, isFlagged: true, flagReason, status: 'flagged' }
                        : job
                ),
                statistics: {
                    ...state.statistics,
                    flaggedJobs: state.statistics.flaggedJobs + jobIds.length
                }
            }));

            return { success: true, message: res.data.message, flagged: jobIds.length };
        } catch (err) {
            return get().handleError(err, 'Bulk flag jobs failed');
        } finally {
            set({ loading: false });
        }
    },

    // TESTS MANAGEMENT
    getTests: async (params = {}) => {
        set({ loading: true, error: null });
        try {
            const queryParams = new URLSearchParams();

            Object.entries(params).forEach(([key, value]) => {
                if (value !== undefined && value !== null && value !== '') {
                    queryParams.append(key, value);
                }
            });

            const url = `${ADMIN_ENDPOINTS.TESTS}${queryParams.toString() ? `?${queryParams}` : ''}`;
            const res = await axiosInstance.get(url);

            set({
                tests: res.data.tests || [],
                testsPagination: res.data.pagination || { current: 1, pages: 1, total: 0 }
            });

            return { success: true, data: res.data };
        } catch (err) {
            return get().handleError(err, 'Fetch tests failed');
        } finally {
            set({ loading: false });
        }
    },

    getTestById: async (testId) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.get(ADMIN_ENDPOINTS.TEST(testId));
            return { success: true, test: res.data.test };
        } catch (err) {
            return get().handleError(err, 'Fetch test failed');
        } finally {
            set({ loading: false });
        }
    },

    flagTest: async (testId, flagReason) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(ADMIN_ENDPOINTS.FLAG_TEST(testId), {
                reason: flagReason
            });

            // Update local state
            set((state) => ({
                tests: state.tests.map(test =>
                    test._id === testId
                        ? { ...test, isFlagged: true, flagReason, status: 'flagged' }
                        : test
                ),
                statistics: {
                    ...state.statistics,
                    flaggedTests: state.statistics.flaggedTests + 1
                }
            }));

            return { success: true, message: res.data.message };
        } catch (err) {
            return get().handleError(err, 'Flag test failed');
        } finally {
            set({ loading: false });
        }
    },

    unflagTest: async (testId) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(`${ADMIN_ENDPOINTS.TEST(testId)}/unflag`);

            // Update local state
            set((state) => ({
                tests: state.tests.map(test =>
                    test._id === testId
                        ? { ...test, isFlagged: false, flagReason: null, status: 'active' }
                        : test
                ),
                statistics: {
                    ...state.statistics,
                    flaggedTests: Math.max(0, state.statistics.flaggedTests - 1)
                }
            }));

            return { success: true, message: res.data.message };
        } catch (err) {
            return get().handleError(err, 'Unflag test failed');
        } finally {
            set({ loading: false });
        }
    },

    getTestAnalytics: async (testId) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.get(`${ADMIN_ENDPOINTS.TEST(testId)}/analytics`);
            return { success: true, analytics: res.data.analytics };
        } catch (err) {
            return get().handleError(err, 'Fetch test analytics failed');
        } finally {
            set({ loading: false });
        }
    },

    // SETTINGS MANAGEMENT
    getSettings: async () => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.get(ADMIN_ENDPOINTS.SETTINGS);
            set({ settings: res.data.settings });
            return { success: true, settings: res.data.settings };
        } catch (err) {
            return get().handleError(err, 'Fetch settings failed');
        } finally {
            set({ loading: false });
        }
    },

    updateSettings: async (settingsData) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.put(ADMIN_ENDPOINTS.SETTINGS, settingsData);
            set({ settings: res.data.settings });
            return { success: true, message: res.data.message, settings: res.data.settings };
        } catch (err) {
            return get().handleError(err, 'Update settings failed');
        } finally {
            set({ loading: false });
        }
    },

    // DASHBOARD & ANALYTICS - Calculate stats from existing data (no API call)
    getDashboardStats: async () => {
        set({ loading: true, error: null });
        try {
            // Get fresh data from existing endpoints
            await Promise.allSettled([
                get().getUsers({ limit: 1000 }), // Get more users for accurate count
                get().getJobs({ limit: 1000 }),  // Get more jobs for accurate count
                get().getTests({ limit: 1000 })  // Get more tests for accurate count
            ]);

            const { users, jobPosts, tests } = get();

            // Calculate statistics from the loaded data
            const statistics = {
                totalUsers: users?.length || 0,
                totalCompanies: users?.filter(user => user.role === 'company')?.length || 0,
                totalJobs: jobPosts?.length || 0,
                totalTests: tests?.length || 0,
                activeUsers: users?.filter(user => user.isActive)?.length || 0,
                activeCompanies: users?.filter(user => user.role === 'company' && user.isActive)?.length || 0,
                pendingCompanies: users?.filter(user => user.role === 'company' && !user.isVerified)?.length || 0,
                flaggedJobs: jobPosts?.filter(job => job.flaggedBy || job.isFlagged)?.length || 0,
                flaggedTests: tests?.filter(test => test.flaggedBy || test.isFlagged)?.length || 0
            };

            set({ statistics });
            return { success: true, statistics };
        } catch (err) {
            // If there's an error, return default statistics
            const defaultStats = {
                totalUsers: 0,
                totalCompanies: 0,
                totalJobs: 0,
                totalTests: 0,
                activeUsers: 0,
                activeCompanies: 0,
                pendingCompanies: 0,
                flaggedJobs: 0,
                flaggedTests: 0
            };
            set({ statistics: defaultStats });
            return { success: true, statistics: defaultStats };
        } finally {
            set({ loading: false });
        }
    },

    getSystemAnalytics: async (period = 'week') => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.get(`${ADMIN_ENDPOINTS.SETTINGS}/analytics?period=${period}`);
            return { success: true, analytics: res.data.analytics };
        } catch (err) {
            return get().handleError(err, 'Fetch system analytics failed');
        } finally {
            set({ loading: false });
        }
    },

    // SEARCH & FILTER UTILITIES
    searchUsers: async (searchTerm, filters = {}) => {
        const params = { search: searchTerm, ...filters };
        return await get().getUsers(params);
    },

    searchCompanies: async (searchTerm, filters = {}) => {
        const params = { search: searchTerm, ...filters };
        return await get().getCompanies(params);
    },

    searchJobs: async (searchTerm, filters = {}) => {
        const params = { search: searchTerm, ...filters };
        return await get().getJobs(params);
    },

    searchTests: async (searchTerm, filters = {}) => {
        const params = { search: searchTerm, ...filters };
        return await get().getTests(params);
    },

    // EXPORT UTILITIES
    exportUsers: async (filters = {}) => {
        set({ loading: true, error: null });
        try {
            const params = new URLSearchParams({ ...filters, export: 'true' });
            const res = await axiosInstance.get(`${ADMIN_ENDPOINTS.USERS}/export?${params}`, {
                responseType: 'blob'
            });

            // Create download link
            const url = window.URL.createObjectURL(new Blob([res.data]));
            const link = document.createElement('a');
            link.href = url;
            link.download = `users-export-${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            return { success: true, message: 'Users exported successfully' };
        } catch (err) {
            return get().handleError(err, 'Export users failed');
        } finally {
            set({ loading: false });
        }
    },

    exportCompanies: async (filters = {}) => {
        set({ loading: true, error: null });
        try {
            const params = new URLSearchParams({ ...filters, export: 'true' });
            const res = await axiosInstance.get(`${ADMIN_ENDPOINTS.COMPANIES}/export?${params}`, {
                responseType: 'blob'
            });

            const url = window.URL.createObjectURL(new Blob([res.data]));
            const link = document.createElement('a');
            link.href = url;
            link.download = `companies-export-${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            return { success: true, message: 'Companies exported successfully' };
        } catch (err) {
            return get().handleError(err, 'Export companies failed');
        } finally {
            set({ loading: false });
        }
    },

    // BULK OPERATIONS
    bulkDeleteUsers: async (userIds) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.delete(`${ADMIN_ENDPOINTS.USERS}/bulk`, {
                data: { userIds }
            });

            // Update local state
            set((state) => ({
                users: state.users.filter(user => !userIds.includes(user._id)),
                statistics: {
                    ...state.statistics,
                    totalUsers: Math.max(0, state.statistics.totalUsers - userIds.length)
                }
            }));

            return { success: true, message: res.data.message, deleted: userIds.length };
        } catch (err) {
            return get().handleError(err, 'Bulk delete users failed');
        } finally {
            set({ loading: false });
        }
    },

    bulkApproveCompanies: async (companyIds) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(`${ADMIN_ENDPOINTS.COMPANIES}/bulk-approve`, {
                companyIds
            });

            // Update local state
            set((state) => ({
                pendingCompanies: state.pendingCompanies.filter(company => !companyIds.includes(company._id)),
                statistics: {
                    ...state.statistics,
                    totalCompanies: state.statistics.totalCompanies + companyIds.length,
                    activeCompanies: state.statistics.activeCompanies + companyIds.length,
                    pendingCompanies: Math.max(0, state.statistics.pendingCompanies - companyIds.length)
                }
            }));

            return { success: true, message: res.data.message, approved: companyIds.length };
        } catch (err) {
            return get().handleError(err, 'Bulk approve companies failed');
        } finally {
            set({ loading: false });
        }
    },

    // AUDIT LOGS
    getAuditLogs: async (params = {}) => {
        set({ loading: true, error: null });
        try {
            const queryParams = new URLSearchParams(params);
            const res = await axiosInstance.get(`${ADMIN_ENDPOINTS.SETTINGS}/audit-logs?${queryParams}`);
            return { success: true, logs: res.data.logs, pagination: res.data.pagination };
        } catch (err) {
            return get().handleError(err, 'Fetch audit logs failed');
        } finally {
            set({ loading: false });
        }
    },

    // SYSTEM HEALTH
    getSystemHealth: async () => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.get(`${ADMIN_ENDPOINTS.SETTINGS}/system-health`);
            return { success: true, health: res.data.health };
        } catch (err) {
            return get().handleError(err, 'Fetch system health failed');
        } finally {
            set({ loading: false });
        }
    },

    // UTILITY FUNCTIONS
    refreshData: async () => {
        const state = get();
        await Promise.all([
            state.getUsers(),
            state.getCompanies(),
            state.getJobs(),
            state.getTests(),
            state.getDashboardStats()
        ]);
    },

    resetState: () => {
        set({
            users: [],
            companies: [],
            pendingCompanies: [],
            jobPosts: [],
            tests: [],
            settings: null,
            loading: false,
            error: null,
            usersPagination: { current: 1, pages: 1, total: 0 },
            companiesPagination: { current: 1, pages: 1, total: 0 },
            jobsPagination: { current: 1, pages: 1, total: 0 },
            testsPagination: { current: 1, pages: 1, total: 0 },
            statistics: {
                totalUsers: 0,
                totalCompanies: 0,
                totalJobs: 0,
                totalTests: 0,
                activeUsers: 0,
                activeCompanies: 0,
                pendingCompanies: 0,
                flaggedJobs: 0,
                flaggedTests: 0
            }
        });
    }
}));

export default useAdminStore;
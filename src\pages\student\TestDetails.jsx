import { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FaCalendarAlt, FaRegClock, FaUser, FaArrowLeft, FaPlay, FaCheckCircle } from 'react-icons/fa';
import useStudentStore from '../../store/studentStore';

const TestDetails = () => {
  const { testId } = useParams();
  const navigate = useNavigate();
  const {
    testDetails,
    loading,
    error,
    fetchTestDetails,
    startTest
  } = useStudentStore();

  useEffect(() => {
    if (testId) {
      fetchTestDetails(testId);
    }
  }, [testId, fetchTestDetails]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} ${mins} minute${mins !== 1 ? 's' : ''}`;
    }
    return `${mins} minute${mins !== 1 ? 's' : ''}`;
  };

  const handleStartTest = async () => {
    try {
      await startTest(testId);
      // Navigate to the legacy test interface for now
      navigate(`/test?testId=${testId}`);
    } catch (error) {
      console.error('Failed to start test:', error);
    }
  };

  const isTestLive = () => {
    if (!testDetails) return false;
    const now = new Date();
    const scheduledDate = new Date(testDetails.scheduledDate);
    const endDate = new Date(testDetails.endDate);
    return now >= scheduledDate && now <= endDate;
  };

  const isTestUpcoming = () => {
    if (!testDetails) return false;
    const now = new Date();
    const scheduledDate = new Date(testDetails.scheduledDate);
    return now < scheduledDate;
  };

  const isTestCompleted = () => {
    if (!testDetails) return false;
    return testDetails.participantStatus === 'submitted' || testDetails.participantStatus === 'evaluated';
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 max-w-4xl mx-auto">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
        <button
          onClick={() => navigate('/student/tests')}
          className="flex items-center gap-2 text-blue-600 hover:text-blue-800"
        >
          <FaArrowLeft /> Back to Tests
        </button>
      </div>
    );
  }

  if (!testDetails) {
    return (
      <div className="p-8 max-w-4xl mx-auto">
        <div className="text-center py-12">
          <h3 className="text-xl font-semibold text-gray-600 mb-2">Test not found</h3>
          <p className="text-gray-500 mb-4">The test you're looking for doesn't exist or you don't have access to it.</p>
          <button
            onClick={() => navigate('/student/tests')}
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800 mx-auto"
          >
            <FaArrowLeft /> Back to Tests
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/student/tests')}
          className="flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          <FaArrowLeft /> Back to Tests
        </button>
      </div>

      {/* Test Info Card */}
      <div className="bg-white rounded-xl shadow-lg p-8 mb-6">
        <div className="flex items-start justify-between mb-6">
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-[#23414c] mb-2">{testDetails.testName}</h1>
            <p className="text-gray-600 text-lg mb-4">{testDetails.description}</p>

            {/* Status Badge */}
            <div className="mb-4">
              {isTestUpcoming() && (
                <span className="inline-block bg-blue-100 text-blue-800 text-sm font-semibold px-3 py-1 rounded-full">
                  Upcoming
                </span>
              )}
              {isTestLive() && (
                <span className="inline-block bg-green-100 text-green-800 text-sm font-semibold px-3 py-1 rounded-full animate-pulse">
                  Live Now
                </span>
              )}
              {isTestCompleted() && (
                <span className="inline-block bg-gray-100 text-gray-800 text-sm font-semibold px-3 py-1 rounded-full">
                  Completed
                </span>
              )}
            </div>
          </div>

          {/* Action Button */}
          <div className="ml-6">
            {isTestLive() && testDetails.participantStatus !== 'submitted' && (
              <button
                onClick={handleStartTest}
                className="bg-[#23414c] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#1a2f36] transition-colors flex items-center gap-2"
              >
                <FaPlay /> Start Test
              </button>
            )}
            {isTestCompleted() && (
              <button
                disabled
                className="bg-gray-300 text-gray-600 px-6 py-3 rounded-lg font-semibold cursor-not-allowed flex items-center gap-2"
              >
                <FaCheckCircle /> Completed
              </button>
            )}
          </div>
        </div>

        {/* Test Details Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-2 text-blue-600 mb-2">
              <FaCalendarAlt />
              <span className="font-semibold">Scheduled Date</span>
            </div>
            <p className="text-gray-800">{formatDate(testDetails.scheduledDate)}</p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-2 text-red-600 mb-2">
              <FaCalendarAlt />
              <span className="font-semibold">End Date</span>
            </div>
            <p className="text-gray-800">{formatDate(testDetails.endDate)}</p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-2 text-green-600 mb-2">
              <FaRegClock />
              <span className="font-semibold">Duration</span>
            </div>
            <p className="text-gray-800">{formatDuration(testDetails.duration)}</p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-2 text-purple-600 mb-2">
              <FaUser />
              <span className="font-semibold">Questions</span>
            </div>
            <p className="text-gray-800">{testDetails.questions?.length || 0} Questions</p>
          </div>
        </div>

        {/* Instructions */}
        {testDetails.instructions && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
            <h3 className="font-bold text-lg text-yellow-800 mb-3">Instructions</h3>
            <div className="text-yellow-700 whitespace-pre-wrap">{testDetails.instructions}</div>
          </div>
        )}

        {/* Test Settings */}
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-bold text-lg text-gray-800 mb-3">Test Settings</h3>
            <ul className="space-y-2 text-gray-600">
              <li>• Allowed Attempts: {testDetails.allowedAttempts || 1}</li>
              <li>• Questions Order: {testDetails.randomizeQuestions ? 'Randomized' : 'Fixed'}</li>
              <li>• Results: {testDetails.showResults ? 'Visible after completion' : 'Hidden'}</li>
            </ul>
          </div>

          {testDetails.questions && testDetails.questions.length > 0 && (
            <div>
              <h3 className="font-bold text-lg text-gray-800 mb-3">Question Preview</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-600 mb-2">Sample Question:</p>
                <p className="text-gray-800 font-medium">
                  {testDetails.questions[0]?.questionText || 'Questions will be shown during the test'}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TestDetails;
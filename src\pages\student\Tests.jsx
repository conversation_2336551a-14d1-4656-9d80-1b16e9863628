import { useState, useEffect } from 'react';
import { FaUserFriends, FaCalendarAlt, FaRegClock, FaUser, FaHistory, FaClock } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import useStudentStore from '../../store/studentStore';

export default function Tests() {
  const navigate = useNavigate();
  const {
    tests,
    testsLoading,
    testsError,
    fetchUpcomingTests,
    fetchLiveTests,
    fetchTestHistory,
    startTest
  } = useStudentStore();

  const [activeTab, setActiveTab] = useState('upcoming');

  useEffect(() => {
    // Fetch all test data on component mount
    fetchUpcomingTests();
    fetchLiveTests();
    fetchTestHistory();
  }, [fetchUpcomingTests, fetchLiveTests, fetchTestHistory]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const handleStartTest = async (testId) => {
    try {
      await startTest(testId);
      navigate(`/student/test/${testId}`);
    } catch (error) {
      console.error('Failed to start test:', error);
    }
  };

  const handleViewDetails = (testId) => {
    navigate(`/student/test-details/${testId}`);
  };

  const getCurrentTests = () => {
    switch (activeTab) {
      case 'upcoming':
        return tests.upcoming || [];
      case 'live':
        return tests.live || [];
      case 'history':
        return tests.history || [];
      default:
        return [];
    }
  };

  const currentTests = getCurrentTests();

  if (testsLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <h1 className="text-3xl font-bold text-[#23414c] mb-6">My Tests</h1>

      {testsError && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {testsError}
        </div>
      )}

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg w-fit">
        <button
          onClick={() => setActiveTab('upcoming')}
          className={`px-4 py-2 rounded-md font-medium transition-colors ${
            activeTab === 'upcoming'
              ? 'bg-white text-[#23414c] shadow-sm'
              : 'text-gray-600 hover:text-[#23414c]'
          }`}
        >
          <FaClock className="inline mr-2" />
          Upcoming ({tests.upcoming?.length || 0})
        </button>
        <button
          onClick={() => setActiveTab('live')}
          className={`px-4 py-2 rounded-md font-medium transition-colors ${
            activeTab === 'live'
              ? 'bg-white text-[#23414c] shadow-sm'
              : 'text-gray-600 hover:text-[#23414c]'
          }`}
        >
          <FaRegClock className="inline mr-2" />
          Live ({tests.live?.length || 0})
        </button>
        <button
          onClick={() => setActiveTab('history')}
          className={`px-4 py-2 rounded-md font-medium transition-colors ${
            activeTab === 'history'
              ? 'bg-white text-[#23414c] shadow-sm'
              : 'text-gray-600 hover:text-[#23414c]'
          }`}
        >
          <FaHistory className="inline mr-2" />
          History ({tests.history?.length || 0})
        </button>
      </div>

      {/* Test List */}
      {currentTests.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">
            {activeTab === 'upcoming' && <FaClock />}
            {activeTab === 'live' && <FaRegClock />}
            {activeTab === 'history' && <FaHistory />}
          </div>
          <h3 className="text-xl font-semibold text-gray-600 mb-2">
            No {activeTab} tests found
          </h3>
          <p className="text-gray-500">
            {activeTab === 'upcoming' && "You don't have any upcoming tests scheduled."}
            {activeTab === 'live' && "No tests are currently live."}
            {activeTab === 'history' && "You haven't taken any tests yet."}
          </p>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {currentTests.map((test) => (
            <div key={test._id} className="bg-white rounded-xl shadow-lg p-6 border border-gray-200 hover:shadow-xl transition-shadow">
              {/* Company Info */}
              <div className="flex items-center gap-3 mb-4">
                {test.companyId?.logo && (
                  <img
                    src={test.companyId.logo}
                    alt={test.companyId.name}
                    className="w-12 h-12 rounded-lg object-cover"
                  />
                )}
                <div>
                  <h3 className="font-bold text-lg text-[#23414c]">{test.testName}</h3>
                  <p className="text-sm text-gray-600">{test.companyId?.name}</p>
                </div>
              </div>

              {/* Test Description */}
              {test.description && (
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">{test.description}</p>
              )}

              {/* Test Info */}
              <div className="space-y-2 mb-4">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <FaCalendarAlt className="text-blue-500" />
                  <span>
                    {activeTab === 'upcoming' && `Scheduled: ${formatDate(test.scheduledDate)}`}
                    {activeTab === 'live' && `Ends: ${formatDate(test.endDate)}`}
                    {activeTab === 'history' && `Completed: ${formatDate(test.endDate)}`}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <FaRegClock className="text-green-500" />
                  <span>Duration: {formatDuration(test.duration)}</span>
                </div>
                {test.associatedJobs && test.associatedJobs.length > 0 && (
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <FaUser className="text-purple-500" />
                    <span>For: {test.associatedJobs.map(job => job.title).join(', ')}</span>
                  </div>
                )}
              </div>

              {/* Status Badge */}
              <div className="mb-4">
                {activeTab === 'upcoming' && (
                  <span className="inline-block bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded-full">
                    Upcoming
                  </span>
                )}
                {activeTab === 'live' && (
                  <span className="inline-block bg-green-100 text-green-800 text-xs font-semibold px-2 py-1 rounded-full animate-pulse">
                    Live Now
                  </span>
                )}
                {activeTab === 'history' && (
                  <span className="inline-block bg-gray-100 text-gray-800 text-xs font-semibold px-2 py-1 rounded-full">
                    Completed
                  </span>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                <button
                  onClick={() => handleViewDetails(test._id)}
                  className="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors"
                >
                  View Details
                </button>
                {activeTab === 'live' && (
                  <button
                    onClick={() => handleStartTest(test._id)}
                    className="flex-1 bg-[#23414c] text-white px-4 py-2 rounded-lg font-medium hover:bg-[#1a2f36] transition-colors flex items-center justify-center gap-2"
                  >
                    <FaUserFriends />
                    Start Test
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}


import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Briefcase,
  Search,
  Filter,
  MoreVertical,
  Flag,
  Eye,
  Download,
  RefreshCw,
  AlertTriangle,
  MapPin,
  DollarSign,
  Calendar,
  Users,
  Building2,
  Clock,
  CheckCircle,
  XCircle,
  TrendingUp
} from 'lucide-react';
import useAdminStore from '../../store/adminStore';

const AdminJobPosts = () => {
  // Admin store integration
  const {
    jobPosts,
    jobsPagination,
    loading,
    error,
    getJobs,
    getJobById,
    flagJob,
    unflagJob,
    bulkFlagJobs,
    searchJobs,
    clearError
  } = useAdminStore();

  // Local state
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    status: '',
    jobType: '',
    location: '',
    company: '',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });
  const [selectedJobs, setSelectedJobs] = useState([]);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedJob, setSelectedJob] = useState(null);
  const [showJobModal, setShowJobModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [flagReason, setFlagReason] = useState('');
  const [showFlagModal, setShowFlagModal] = useState(false);
  const [jobToFlag, setJobToFlag] = useState(null);

  // Load jobs on component mount and when filters change
  useEffect(() => {
    const loadJobs = async () => {
      try {
        const result = await getJobs({
          page: currentPage,
          limit: 10,
          search: searchTerm,
          ...filters
        });
        console.log('Jobs loaded:', result); // Debug log
      } catch (error) {
        console.error('Failed to load jobs:', error);
      }
    };

    loadJobs();
  }, [currentPage, searchTerm, filters]);

  // Handler functions
  const handleSearch = async (e) => {
    e.preventDefault();
    setCurrentPage(1);
    await searchJobs(searchTerm, filters);
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  const handleJobSelect = (jobId) => {
    setSelectedJobs(prev =>
      prev.includes(jobId)
        ? prev.filter(id => id !== jobId)
        : [...prev, jobId]
    );
  };

  const handleSelectAll = () => {
    if (selectedJobs.length === jobPosts.length) {
      setSelectedJobs([]);
    } else {
      setSelectedJobs(jobPosts.map(job => job._id));
    }
  };

  const handleJobAction = async (action, jobId) => {
    try {
      switch (action) {
        case 'flag':
          setJobToFlag(jobId);
          setShowFlagModal(true);
          break;
        case 'unflag':
          await unflagJob(jobId);
          break;
        case 'view':
          const jobResult = await getJobById(jobId);
          if (jobResult.success) {
            setSelectedJob(jobResult.job);
            setShowJobModal(true);
          }
          break;
        default:
          break;
      }
    } catch (error) {
      console.error('Job action failed:', error);
    }
  };

  const handleFlagSubmit = async () => {
    if (!flagReason.trim() || !jobToFlag) return;

    try {
      await flagJob(jobToFlag, flagReason);
      setShowFlagModal(false);
      setFlagReason('');
      setJobToFlag(null);
    } catch (error) {
      console.error('Flag job failed:', error);
    }
  };

  const handleBulkFlag = async () => {
    if (selectedJobs.length === 0) return;

    const reason = prompt('Enter flag reason:');
    if (!reason) return;

    try {
      await bulkFlagJobs(selectedJobs, reason);
      setSelectedJobs([]);
    } catch (error) {
      console.error('Bulk flag failed:', error);
    }
  };

  const handleRefresh = async () => {
    await getJobs({
      page: currentPage,
      limit: 10,
      search: searchTerm,
      ...filters
    });
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <motion.div
      className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-gray-100 p-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div className="mb-8" variants={itemVariants}>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Job Posts Management</h1>
          <p className="text-gray-600">Monitor and manage job postings across the platform</p>
        </motion.div>

        {/* Error Display */}
        {error && (
          <motion.div
            className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="flex items-center gap-2">
              <AlertTriangle className="text-red-500" size={20} />
              <span className="text-red-700 font-medium">{error}</span>
              <button
                onClick={clearError}
                className="ml-auto text-red-500 hover:text-red-700"
              >
                ×
              </button>
            </div>
          </motion.div>
        )}

        <motion.div
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6"
          variants={itemVariants}
        >
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            {/* Search */}
            <form onSubmit={handleSearch} className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Search job posts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </form>

            {/* Action Buttons */}
            <div className="flex gap-2 flex-wrap">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                <Filter size={16} />
                Filters
              </button>

              <button
                onClick={handleRefresh}
                disabled={loading}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
              >
                <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
                Refresh
              </button>

              <button
                onClick={() => {/* Export functionality */ }}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                <Download size={16} />
                Export
              </button>
            </div>
          </div>

          {/* Filters Panel */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                className="mt-4 pt-4 border-t border-gray-200"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
              >
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                  <select
                    value={filters.status}
                    onChange={(e) => handleFilterChange('status', e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="closed">Closed</option>
                    <option value="flagged">Flagged</option>
                    <option value="draft">Draft</option>
                  </select>

                  <select
                    value={filters.jobType}
                    onChange={(e) => handleFilterChange('jobType', e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">All Types</option>
                    <option value="full-time">Full Time</option>
                    <option value="part-time">Part Time</option>
                    <option value="contract">Contract</option>
                    <option value="internship">Internship</option>
                  </select>

                  <select
                    value={filters.location}
                    onChange={(e) => handleFilterChange('location', e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">All Locations</option>
                    <option value="remote">Remote</option>
                    <option value="onsite">On-site</option>
                    <option value="hybrid">Hybrid</option>
                  </select>

                  <select
                    value={filters.sortBy}
                    onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="createdAt">Created Date</option>
                    <option value="title">Job Title</option>
                    <option value="company">Company</option>
                    <option value="applicationsCount">Applications</option>
                  </select>

                  <select
                    value={filters.sortOrder}
                    onChange={(e) => handleFilterChange('sortOrder', e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="desc">Descending</option>
                    <option value="asc">Ascending</option>
                  </select>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Bulk Actions */}
        {selectedJobs.length > 0 && (
          <motion.div
            className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="flex items-center justify-between">
              <span className="text-orange-700 font-medium">
                {selectedJobs.length} job(s) selected
              </span>
              <div className="flex gap-2">
                <button
                  onClick={handleBulkFlag}
                  className="px-4 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                >
                  Bulk Flag
                </button>
                <button
                  onClick={() => setSelectedJobs([])}
                  className="px-4 py-1 border border-orange-300 rounded text-sm hover:bg-orange-50"
                >
                  Clear
                </button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Jobs Table */}
        <motion.div
          className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
          variants={itemVariants}
        >
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : jobPosts.length === 0 ? (
            <div className="text-center py-12">
              <Briefcase className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No job posts found</h3>
              <p className="text-gray-500">Try adjusting your search or filters</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left">
                      <input
                        type="checkbox"
                        checked={selectedJobs.length === jobPosts.length && jobPosts.length > 0}
                        onChange={handleSelectAll}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Job Details
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Company
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type & Location
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Applications
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {jobPosts.map((job) => (
                    <motion.tr
                      key={job._id}
                      className="hover:bg-gray-50"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <td className="px-6 py-4">
                        <input
                          type="checkbox"
                          checked={selectedJobs.includes(job._id)}
                          onChange={() => handleJobSelect(job._id)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-6 py-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {job.title || job.role || 'N/A'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {job.salary || job.salaryRange || 'Salary not specified'}
                          </div>
                          <div className="text-xs text-gray-400">
                            {job.category || 'No category'}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8">
                            <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                              <Building2 size={16} className="text-gray-600" />
                            </div>
                          </div>
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-900">
                              {job.company?.name || job.companyName || job.companyId?.companyName || 'N/A'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900">
                          {job.jobType || job.type || 'N/A'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {job.location || job.workMode || 'N/A'}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${job.flaggedBy ? 'bg-red-100 text-red-800' :
                          job.isActive ? 'bg-green-100 text-green-800' :
                            job.status === 'active' ? 'bg-green-100 text-green-800' :
                              job.status === 'closed' ? 'bg-gray-100 text-gray-800' :
                                job.status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-blue-100 text-blue-800'
                          }`}>
                          {job.flaggedBy ? 'Flagged' : (job.isActive ? 'Active' : (job.status || 'Active'))}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900">
                        <div className="flex items-center gap-1">
                          <Users size={14} />
                          {job.currentApplications || job.applicationsCount || 0}
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500">
                        {job.createdAt ? new Date(job.createdAt).toLocaleDateString() : 'N/A'}
                      </td>
                      <td className="px-6 py-4 text-sm font-medium">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleJobAction('view', job._id)}
                            className="text-blue-600 hover:text-blue-900"
                            title="View Details"
                          >
                            <Eye size={16} />
                          </button>

                          {job.flaggedBy || job.isFlagged ? (
                            <button
                              onClick={() => handleJobAction('unflag', job._id)}
                              className="text-green-600 hover:text-green-900"
                              title="Unflag"
                            >
                              <CheckCircle size={16} />
                            </button>
                          ) : (
                            <button
                              onClick={() => handleJobAction('flag', job._id)}
                              className="text-red-600 hover:text-red-900"
                              title="Flag"
                            >
                              <Flag size={16} />
                            </button>
                          )}
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </motion.div>

        {/* Pagination */}
        {jobsPagination.pages > 1 && (
          <motion.div
            className="mt-6 flex justify-center"
            variants={itemVariants}
          >
            <div className="flex gap-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>

              {Array.from({ length: jobsPagination.pages }, (_, i) => i + 1).map(page => (
                <button
                  key={page}
                  onClick={() => setCurrentPage(page)}
                  className={`px-3 py-2 border rounded-lg ${currentPage === page
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'border-gray-300 hover:bg-gray-50'
                    }`}
                >
                  {page}
                </button>
              ))}

              <button
                onClick={() => setCurrentPage(prev => Math.min(jobsPagination.pages, prev + 1))}
                disabled={currentPage === jobsPagination.pages}
                className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </motion.div>
        )}

        {/* Flag Modal */}
        <AnimatePresence>
          {showFlagModal && (
            <motion.div
              className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setShowFlagModal(false)}
            >
              <motion.div
                className="bg-white rounded-lg shadow-xl max-w-md w-full"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                onClick={(e) => e.stopPropagation()}
              >
                <div className="p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Flag Job Post</h3>
                  <textarea
                    value={flagReason}
                    onChange={(e) => setFlagReason(e.target.value)}
                    placeholder="Enter reason for flagging this job post..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    rows={4}
                  />
                  <div className="flex gap-3 mt-4">
                    <button
                      onClick={handleFlagSubmit}
                      disabled={!flagReason.trim()}
                      className="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 disabled:opacity-50"
                    >
                      Flag Job
                    </button>
                    <button
                      onClick={() => setShowFlagModal(false)}
                      className="flex-1 border border-gray-300 py-2 px-4 rounded-lg hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Job Details Modal */}
        <AnimatePresence>
          {showJobModal && selectedJob && (
            <motion.div
              className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setShowJobModal(false)}
            >
              <motion.div
                className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                onClick={(e) => e.stopPropagation()}
              >
                <div className="p-6">
                  <div className="flex justify-between items-start mb-6">
                    <h2 className="text-2xl font-bold text-gray-900">Job Details</h2>
                    <button
                      onClick={() => setShowJobModal(false)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      ×
                    </button>
                  </div>

                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {selectedJob.title || selectedJob.role}
                      </h3>
                      <p className="text-gray-600">
                        {selectedJob.company?.name || selectedJob.companyName || selectedJob.companyId?.companyName}
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Job Type</label>
                        <p className="text-gray-900">{selectedJob.jobType || selectedJob.type || 'N/A'}</p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                        <p className="text-gray-900">{selectedJob.location || selectedJob.workMode || 'N/A'}</p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                        <p className="text-gray-900">{selectedJob.category || 'N/A'}</p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Experience Level</label>
                        <p className="text-gray-900">{selectedJob.experienceLevel || 'N/A'}</p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Salary</label>
                        <p className="text-gray-900">{selectedJob.salary || selectedJob.salaryRange || 'Not specified'}</p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Applications</label>
                        <p className="text-gray-900">{selectedJob.currentApplications || selectedJob.applicationsCount || 0}</p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${selectedJob.flaggedBy ? 'bg-red-100 text-red-800' :
                          selectedJob.isActive ? 'bg-green-100 text-green-800' :
                            selectedJob.status === 'active' ? 'bg-green-100 text-green-800' :
                              'bg-gray-100 text-gray-800'
                          }`}>
                          {selectedJob.flaggedBy ? 'Flagged' : (selectedJob.isActive ? 'Active' : (selectedJob.status || 'Active'))}
                        </span>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Created</label>
                        <p className="text-gray-900">
                          {selectedJob.createdAt ? new Date(selectedJob.createdAt).toLocaleDateString() : 'N/A'}
                        </p>
                      </div>
                    </div>

                    {selectedJob.description && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <div className="text-gray-900 whitespace-pre-wrap">{selectedJob.description}</div>
                      </div>
                    )}

                    {selectedJob.requirements && selectedJob.requirements.length > 0 && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Requirements</label>
                        <div className="text-gray-900">
                          {Array.isArray(selectedJob.requirements) ? (
                            <ul className="list-disc list-inside space-y-1">
                              {selectedJob.requirements.map((req, index) => (
                                <li key={index}>{req}</li>
                              ))}
                            </ul>
                          ) : (
                            <div className="whitespace-pre-wrap">{selectedJob.requirements}</div>
                          )}
                        </div>
                      </div>
                    )}

                    {selectedJob.techStack && selectedJob.techStack.length > 0 && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Tech Stack</label>
                        <div className="flex flex-wrap gap-2">
                          {selectedJob.techStack.map((tech, index) => (
                            <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                              {tech}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {selectedJob.applicationDeadline && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Application Deadline</label>
                        <p className="text-gray-900">
                          {new Date(selectedJob.applicationDeadline).toLocaleDateString()}
                        </p>
                      </div>
                    )}

                    {(selectedJob.flaggedBy || selectedJob.isFlagged) && selectedJob.flagReason && (
                      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <label className="block text-sm font-medium text-red-700 mb-1">Flag Reason</label>
                        <p className="text-red-900">{selectedJob.flagReason}</p>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

export default AdminJobPosts;